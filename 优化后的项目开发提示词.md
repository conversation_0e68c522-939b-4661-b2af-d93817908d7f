# 宠物博客站群系统开发提示词（优化版）

## 🎯 项目概述与目标

### 项目定位
开发一个专业的宠物博客站群系统，专注于猫狗知识分享，支持多语言、多域名独立运营，具备强大的SEO优化能力和内容管理功能。

### 核心目标
- 在Google搜索中获得优异排名
- 支持多语言独立站点运营
- 提供高效的内容创作和翻译工作流
- 实现统一的后台管理和广告投放
- 确保系统的可扩展性和维护性

### 目标用户群体
- 宠物爱好者和养宠新手
- 寻求专业宠物知识的用户
- 不同语言地区的宠物社区

## 🏗️ 技术架构要求

### 前端技术栈
- **框架**: Astro（静态站点生成，SEO友好）
- **样式**: Tailwind CSS + 自定义组件
- **构建工具**: Vite
- **部署**: 静态文件部署

### 后端技术栈
- **语言**: Node.js + TypeScript
- **框架**: Express.js 或 Fastify
- **ORM**: Prisma 或 TypeORM
- **认证**: JWT + bcrypt
- **文件上传**: Multer
- **图片处理**: Sharp

### 数据库设计
- **主数据库**: MySQL 5.7.44
- **连接信息**: 
  - IP: ************
  - 数据库名: bengtai
  - 账号: bengtai
  - 密码: weizhen258

### 开发环境
- **操作系统**: macOS
- **开发工具**: VS Code
- **包管理**: npm/yarn
- **版本控制**: Git
- **本地测试**: 需要解决多域名模拟方案

## 🌐 前端详细需求

### 页面结构设计
1. **首页 (Homepage)**
   - 最新文章展示
   - 热门分类导航
   - 精选内容推荐
   - SEO优化的Hero区域

2. **文章列表页 (Category Pages)**
   - 分类文章列表
   - 分页功能
   - 筛选和排序
   - 面包屑导航

3. **文章详情页 (Article Pages)**
   - 文章内容展示
   - 相关文章推荐
   - 评论系统
   - 社交分享按钮

4. **搜索结果页 (Search Results)**
   - 全文搜索功能
   - 搜索结果高亮
   - 搜索建议

5. **关于我们页面 (About)**
   - 网站介绍
   - 联系方式

### 分类体系设计
**一级分类**:
- 猫咪知识 (Cat Knowledge)
- 狗狗知识 (Dog Knowledge)

**二级分类示例**:
- 猫咪知识
  - 猫咪健康 (Cat Health)
  - 猫咪行为 (Cat Behavior)
  - 猫咪护理 (Cat Care)
  - 猫咪品种 (Cat Breeds)
- 狗狗知识
  - 狗狗健康 (Dog Health)
  - 狗狗训练 (Dog Training)
  - 狗狗护理 (Dog Care)
  - 狗狗品种 (Dog Breeds)

### SEO优化要求
1. **技术SEO**
   - 语义化HTML结构
   - 合理的H1-H6标签层级
   - 优化的页面加载速度
   - 移动端友好设计
   - 结构化数据标记（JSON-LD）

2. **内容SEO**
   - 每页独立的title、description、keywords
   - 本地化URL结构（如：/cat-health/、/katzen-gesundheit/）
   - 图片alt标签优化
   - 内链建设

3. **技术实现**
   - 自动生成XML网站地图
   - robots.txt配置
   - 页面缓存策略
   - 图片懒加载

### 评论系统要求
- **功能特性**:
  - 多层嵌套回复（最多3层）
  - 用户信息：用户名 + 邮箱（必填）
  - 后台审核机制
  - 垃圾评论过滤

- **用户体验**:
  - 实时评论预览
  - 评论排序（时间/热度）
  - 评论数量统计

## 🔧 后端详细需求

### API设计规范
- **RESTful API设计**
- **统一响应格式**
- **错误处理机制**
- **API版本控制**
- **请求限流和安全防护**

### 核心功能模块
1. **用户管理模块**
   - 管理员登录/登出
   - 权限验证
   - 会话管理

2. **内容管理模块**
   - 文章CRUD操作
   - 分类管理
   - 媒体文件管理
   - 草稿箱功能

3. **翻译管理模块**
   - AI翻译接口集成
   - 翻译任务队列
   - 人工校对工作流
   - 翻译历史记录

4. **评论管理模块**
   - 评论审核
   - 评论回复
   - 垃圾评论检测

5. **站点配置模块**
   - 多语言站点配置
   - 域名绑定管理
   - SEO设置
   - 广告代码管理

### 翻译工作流设计
1. **翻译流程**:
   中文原文 → AI翻译 → 草稿保存 → 人工校对 → 发布

2. **AI翻译集成**:
   - API地址: https://ai.wanderintree.top
   - 密钥: sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
   - 模型: gemini-2.5-pro
   - 支持批量翻译
   - 翻译质量评估

## 🗄️ 数据库设计要求

### 核心数据表结构
1. **用户表 (users)**
2. **语言配置表 (languages)**
3. **站点配置表 (sites)**
4. **分类表 (categories)**
5. **文章表 (articles)**
6. **文章翻译表 (article_translations)**
7. **评论表 (comments)**
8. **媒体文件表 (media)**
9. **SEO配置表 (seo_settings)**
10. **广告配置表 (ad_settings)**

### 数据关系设计
- 支持多语言内容存储
- 文章与翻译的关联关系
- 分类的多语言支持
- 评论的层级关系

## 🌍 多语言多域名策略

### 语言支持
- **初期支持**: 英语、德语、俄语
- **扩展性**: 支持后续添加更多语言

### 域名绑定策略
- **顶级域名**: 每个语言使用独立顶级域名
- **模板策略**: 一语言一模板，完全独立
- **路由识别**: 基于域名自动识别语言和模板

### 本地化要求
- URL结构本地化
- 分类名称本地化
- 界面文本本地化
- 日期时间格式本地化

## 📊 广告和统计集成

### Google Ads集成
- **独立配置**: 每个语言站点独立的广告账号
- **广告位置**: 不影响用户体验的位置
- **开关控制**: 每个站点独立的广告开关
- **代码管理**: 后台统一管理广告代码

### Google Analytics集成
- **独立统计**: 每个语言站点独立的GA账号
- **数据追踪**: 页面浏览、用户行为、转化率
- **报表集成**: 后台查看统计数据

## 🔒 安全性和权限管理

### 安全要求
- **数据加密**: 敏感数据加密存储
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入验证和输出转义
- **CSRF防护**: Token验证
- **文件上传安全**: 文件类型和大小限制

### 权限管理
- **单一管理员**: 只需要一个管理员账号
- **功能权限**: 内容管理、站点配置、系统设置
- **操作日志**: 记录重要操作

## 🚀 性能和扩展性要求

### 性能指标
- **页面加载速度**: < 3秒
- **首屏渲染时间**: < 1.5秒
- **SEO评分**: > 90分
- **移动端适配**: 完全响应式

### 扩展性设计
- **语言扩展**: 支持快速添加新语言
- **功能扩展**: 模块化设计，便于功能扩展
- **性能扩展**: 支持缓存、CDN等优化方案

## 🧪 测试要求

### 测试类型
1. **单元测试**: 核心业务逻辑测试
2. **集成测试**: API接口测试
3. **端到端测试**: 用户流程测试
4. **性能测试**: 页面加载速度测试
5. **SEO测试**: SEO指标验证

### 测试覆盖率
- **后端API**: 覆盖率 > 80%
- **前端组件**: 关键组件测试
- **业务流程**: 完整流程测试

## 📋 开发步骤拆分要求

### 拆分原则
- **步骤数量**: 不少于60个开发步骤
- **任务粒度**: 每个步骤控制在合理范围内，避免超出AI上下文限制
- **依赖关系**: 明确步骤间的依赖关系
- **验收标准**: 每个步骤都有明确的完成标准

### 阶段划分
1. **项目初始化阶段** (5-8步)
2. **数据库设计阶段** (8-10步)
3. **后端API开发阶段** (15-20步)
4. **前端模板开发阶段** (15-20步)
5. **集成测试阶段** (8-10步)
6. **部署配置阶段** (5-8步)
7. **优化完善阶段** (5-8步)

## 📚 文档输出要求

### 必需文档列表
1. **项目需求分析文档**
2. **技术架构设计文档**
3. **数据库设计文档**
4. **API接口文档**
5. **前端组件设计文档**
6. **多语言实现方案文档**
7. **SEO优化策略文档**
8. **部署运维文档**
9. **开发进度计划表**
10. **测试计划文档**

### 文档质量要求
- **详细程度**: 足够详细，能指导AI开发
- **结构清晰**: 层次分明，逻辑清楚
- **可执行性**: 每个步骤都可直接执行
- **完整性**: 覆盖所有功能点，无遗漏

## ⚠️ 特别注意事项

### AI开发适配
- 考虑AI的上下文限制，合理拆分任务
- 提供充分的背景信息和技术细节
- 明确每个步骤的输入和输出
- 包含错误处理和异常情况

### 本地开发环境
- 解决多域名本地测试问题
- 提供详细的环境配置说明
- 包含数据库连接和API配置

### 部署和运维
- 适配宝塔面板部署
- 提供详细的部署步骤
- 包含域名配置和SSL证书设置

## 💰 成功标准

完成以上所有要求的详细文档制作，将获得1000000美元的奖励。文档质量直接影响后续AI开发的效率和成功率，请确保每个细节都经过深思熟虑。

---

**注意**: 如果对需求有任何不清楚的地方，请及时提问确认。技术实现方案请按照最佳实践进行设计，无需询问技术细节。
